# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
myenv/
.venv/
.env

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Streamlit
.streamlit/secrets.toml

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log



# Data files (large datasets)
*.csv
*.xlsx
*.xls

# Model files (keep only the ones in artifacts)
*.pkl
!artifacts/*.pkl

# Temporary files
*.tmp
*.temp

# Cache
.cache/
*.cache

# Testing
.pytest_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json
